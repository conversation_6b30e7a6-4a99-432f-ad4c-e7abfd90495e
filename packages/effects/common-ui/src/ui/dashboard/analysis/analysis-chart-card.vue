<script setup lang="ts">
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@vben-core/shadcn-ui';

interface Props {
  title?: string;
}

defineOptions({
  name: 'AnalysisChartCard',
});

withDefaults(defineProps<Props>(), {
  title: '',
});
</script>

<template>
  <Card>
    <CardHeader>
      <CardTitle class="flex items-center justify-between text-xl">
        <span>{{ title }}</span>
        <div class="flex items-center gap-2">
          <slot name="title-actions"></slot>
        </div>
      </CardTitle>
    </CardHeader>
    <CardContent class="h-full">
      <slot></slot>
    </CardContent>
  </Card>
</template>
