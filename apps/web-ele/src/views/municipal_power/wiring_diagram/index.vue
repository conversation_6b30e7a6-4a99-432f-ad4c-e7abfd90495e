<script setup lang="ts">
import { AnalysisChartCard, Page } from '@vben/common-ui';
import { ref } from 'vue';
import WiringDiagram from './components/WiringDiagram.vue';
import SiteSelector from '#/components/services/site-selector/index.vue';
import RealTimeLoadChart from './components/RealTimeLoadChart.vue';
import RealTimeElectricityChart from './components/RealTimeElectricityChart.vue';

defineOptions({
  name: 'WiringDiagram',
});

const selectedSite = ref('');

function handleSiteSelected() {}
</script>
<template>
  <Page auto-content-height>
    <div class="flex h-full gap-4 overflow-hidden">
      <div
        class="border-border flex h-full w-1/5 min-w-[400px] shrink-0 flex-col gap-4"
      >
        <AnalysisChartCard title="配电信息" class="flex-1">
          <div class="flex flex-col gap-4">
            <div class="flex flex-col gap-2">
              <span class="text-foreground whitespace-nowrap font-medium"
                >站点名称:</span
              >
              <SiteSelector
                v-model="selectedSite"
                placeholder="请选择站点"
                class="w-48"
                @site-selected="handleSiteSelected"
              />
            </div>
            <div class="flex justify-between">
              <span class="text-muted-foreground">地址:</span>
              <span class="text-foreground font-medium">无锡市江阴市江苏农村微电网研究院有限公司</span>
            </div>
            <div class="flex justify-between">
              <span class="text-muted-foreground">变电电压等级:</span>
              <span class="text-foreground font-medium">10.0kV</span>
            </div>
            <div class="flex justify-between">
              <span class="text-muted-foreground">变压器容量:</span>
              <span class="text-foreground font-medium">500kVA</span>
            </div>
            <div class="flex justify-between">
              <span class="text-muted-foreground">最大需量:</span>
              <span class="text-foreground font-medium">kW</span>
            </div>
            <div class="flex justify-between">
              <span class="text-muted-foreground">变压器数量:</span>
              <span class="text-foreground font-medium">1台</span>
            </div>
          </div>
        </AnalysisChartCard>
        <AnalysisChartCard title="实时负荷" class="flex-1">
          <RealTimeLoadChart />
        </AnalysisChartCard>
        <AnalysisChartCard title="实时电量" class="flex-1">
          <RealTimeElectricityChart />
        </AnalysisChartCard>
      </div>
      <div class="bg-card border-border h-full min-w-max flex-1 rounded-lg">
        <WiringDiagram />
      </div>
    </div>
  </Page>
</template>
