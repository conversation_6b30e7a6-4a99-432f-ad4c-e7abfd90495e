<script setup lang="ts">
import { AnalysisChartCard, Page } from '@vben/common-ui';
import { ref } from 'vue';
import WiringDiagram from './components/WiringDiagram.vue';
import SiteSelector from '#/components/services/site-selector/index.vue';

defineOptions({
  name: 'WiringDiagram',
});

const selectedSite = ref('');

function handleSiteSelected() {}
</script>
<template>
  <Page auto-content-height>
    <div class="flex h-full gap-4 overflow-hidden">
      <div
        class="border-border flex h-full w-1/5 min-w-[400px] shrink-0 flex-col gap-4"
      >
        <AnalysisChartCard title="配电信息" class="flex-1">
          <div class="flex flex-col gap-4">
            <div class="flex flex-col gap-2">
              <span class="text-foreground whitespace-nowrap font-medium"
                >站点名称:</span
              >
              <SiteSelector
                v-model="selectedSite"
                placeholder="请选择站点"
                class="w-48"
                @site-selected="handleSiteSelected"
              />
            </div>
            <div class="flex">
              <span>地址:</span>
              <span></span>
            </div>
            <div class="flex">
              <span>变电电压等级:</span>
              <span></span>
            </div>
            <div class="flex">
              <span>变压器容量:</span>
              <span></span>
            </div>
            <div class="flex">
              <span>最大需量:</span>
              <span></span>
            </div>
            <div class="flex">
              <span>变压器数量:</span>
              <span></span>
            </div>
          </div>
        </AnalysisChartCard>
        <AnalysisChartCard title="实时负荷" class="flex-1" />
        <AnalysisChartCard title="实时电量" class="flex-1" />
      </div>
      <div class="bg-card border-border h-full min-w-max flex-1 rounded-lg">
        <WiringDiagram />
      </div>
    </div>
  </Page>
</template>
