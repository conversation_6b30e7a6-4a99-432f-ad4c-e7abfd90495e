<script lang="ts" setup>
import { onMounted, ref } from 'vue';

import {
  EchartsUI,
  type EchartsUIType,
  useEcharts,
} from '@vben/plugins/echarts';

const chartRef = ref<EchartsUIType>();
const { renderEcharts } = useEcharts(chartRef);

onMounted(() => {
  // 生成时间和数据，参考您提供的配置
  const base = +new Date();
  const oneMinute = 60 * 1000; // 每分钟一个数据点
  const timeData = [];
  const loadData = [Math.random() * 300]; // 初始随机值

  // 生成10小时的数据（600个点）
  for (let i = 1; i < 600; i++) {
    const now = new Date(base + i * oneMinute);
    timeData.push([
      now.getHours().toString().padStart(2, '0'),
      now.getMinutes().toString().padStart(2, '0')
    ].join(':'));
    // 生成带有毛刺效果的随机数据
    loadData.push(Math.round((Math.random() - 0.5) * 40 + loadData[i - 1]));
  }

  renderEcharts({
    grid: {
      bottom: '15%',
      containLabel: true,
      left: '5%',
      right: '5%',
      top: '10%',
    },
    legend: {
      bottom: '5%',
      data: ['P', 'Pa', 'Pb', 'Pc'],
      textStyle: {
        fontSize: 10,
      },
    },
    // Y轴单位标注
    graphic: [
      {
        type: 'text',
        left: '3%',
        top: '8%',
        style: {
          text: 'kW',
          fontSize: 10,
          fill: '#666',
        },
      },
    ],
    tooltip: {
      trigger: 'axis',
      position: function (pt: any) {
        return [pt[0], '10%'];
      },
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: timeData,
      axisLabel: {
        fontSize: 9,
        interval: 59, // 每小时显示一个标签
      },
    },
    yAxis: {
      type: 'value',
      boundaryGap: [0, '100%'],
      axisLabel: {
        fontSize: 9,
        formatter: '{value}',
      },
      splitLine: {
        lineStyle: {
          color: '#f3f4f6',
          type: 'dashed',
        },
      },
    },
    series: [
      {
        name: '负荷',
        type: 'line',
        symbol: 'none',
        sampling: 'lttb',
        itemStyle: {
          color: '#ff6b35', // 橙色
        },
        lineStyle: {
          color: '#ff6b35',
          width: 1,
        },
        data: loadData,
      },
    ],

  });
});
</script>

<template>
  <EchartsUI ref="chartRef" height="100%" />
</template>
