<script lang="ts" setup>
import { onMounted, ref } from 'vue';

import {
  EchartsUI,
  type EchartsUIType,
  useEcharts,
} from '@vben/plugins/echarts';

const chartRef = ref<EchartsUIType>();
const { renderEcharts } = useEcharts(chartRef);

onMounted(() => {
  // 生成时间和数据，参考您提供的配置
  const base = +new Date();
  const oneMinute = 60 * 1000; // 每分钟一个数据点
  const timeData = [];

  // 为四条线生成初始数据
  const pData = [Math.random() * 300];
  const paData = [Math.random() * 250];
  const pbData = [Math.random() * 250];
  const pcData = [Math.random() * 250];

  // 生成约50分钟的数据（50个点）
  for (let i = 1; i < 50; i++) {
    const now = new Date(base + i * oneMinute);
    timeData.push([
      now.getHours().toString().padStart(2, '0'),
      now.getMinutes().toString().padStart(2, '0')
    ].join(':'));

    // 为每条线生成带有毛刺效果的随机数据，确保不为负值
    pData.push(Math.max(0, Math.round((Math.random() - 0.5) * 40 + pData[i - 1])));
    paData.push(Math.max(0, Math.round((Math.random() - 0.5) * 35 + paData[i - 1])));
    pbData.push(Math.max(0, Math.round((Math.random() - 0.5) * 35 + pbData[i - 1])));
    pcData.push(Math.max(0, Math.round((Math.random() - 0.5) * 35 + pcData[i - 1])));
  }

  renderEcharts({
    grid: {
      bottom: '15%',
      containLabel: true,
      left: '5%',
      right: '5%',
      top: '20%', // 为上方图例留出空间
    },
    legend: {
      top: '5%',
      left: 'center',
      data: [
        {
          name: 'P',
          textStyle: {
            color: '#ff6b35', // 橙色
          },
        },
        {
          name: 'Pa',
          textStyle: {
            color: '#4ade80', // 绿色
          },
        },
        {
          name: 'Pb',
          textStyle: {
            color: '#3b82f6', // 蓝色
          },
        },
        {
          name: 'Pc',
          textStyle: {
            color: '#8b5cf6', // 紫色
          },
        },
      ],
      textStyle: {
        fontSize: 10,
      },
      // 使用默认的图例尺寸，与下方图表保持一致
    },
    // Y轴单位标注
    graphic: [
      {
        type: 'text',
        left: '3%',
        top: '18%', // 调整位置避免与图例重叠
        style: {
          text: 'kW',
          fontSize: 10,
          fill: '#666',
        },
      },
    ],
    tooltip: {
      trigger: 'axis',
      position: function (pt: any) {
        return [pt[0], '10%'];
      },
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: timeData,
      axisLabel: {
        fontSize: 9,
        interval: 9, // 每10个点显示一个标签
      },
    },
    yAxis: {
      type: 'value',
      min: 0, // 设置最小值为0，去掉负轴
      axisLabel: {
        fontSize: 9,
        formatter: '{value}',
      },
      splitLine: {
        lineStyle: {
          color: '#f3f4f6',
          type: 'dashed',
        },
      },
    },
    series: [
      {
        name: 'P',
        type: 'line',
        symbol: 'none',
        sampling: 'lttb',
        itemStyle: {
          color: '#ff6b35', // 橙色
        },
        lineStyle: {
          color: '#ff6b35',
          width: 1,
        },
        data: pData,
      },
      {
        name: 'Pa',
        type: 'line',
        symbol: 'none',
        sampling: 'lttb',
        itemStyle: {
          color: '#4ade80', // 绿色
        },
        lineStyle: {
          color: '#4ade80',
          width: 1,
        },
        data: paData,
      },
      {
        name: 'Pb',
        type: 'line',
        symbol: 'none',
        sampling: 'lttb',
        itemStyle: {
          color: '#3b82f6', // 蓝色
        },
        lineStyle: {
          color: '#3b82f6',
          width: 1,
        },
        data: pbData,
      },
      {
        name: 'Pc',
        type: 'line',
        symbol: 'none',
        sampling: 'lttb',
        itemStyle: {
          color: '#8b5cf6', // 紫色
        },
        lineStyle: {
          color: '#8b5cf6',
          width: 1,
        },
        data: pcData,
      },
    ],

  });
});
</script>

<template>
  <EchartsUI ref="chartRef" height="100%" />
</template>
