<script lang="ts" setup>
import { onMounted, ref } from 'vue';

import {
  EchartsUI,
  type EchartsUIType,
  useEcharts,
} from '@vben/plugins/echarts';

const chartRef = ref<EchartsUIType>();
const { renderEcharts } = useEcharts(chartRef);

onMounted(() => {
  renderEcharts({
    grid: {
      bottom: '15%',
      containLabel: true,
      left: '8%',
      right: '5%',
      top: '15%',
    },
    legend: {
      bottom: '5%',
      data: ['P', 'Pa', 'Pb', 'Pc'],
      textStyle: {
        fontSize: 10,
      },
    },
    // Y轴单位标注
    graphic: [
      {
        type: 'text',
        left: '3%',
        top: '8%',
        style: {
          text: 'kW',
          fontSize: 10,
          fill: '#666',
        },
      },
    ],
    series: [
      {
        name: '负荷',
        data: [
          120, 115, 110, 108, 105, 102, 100, 98, 95, 92, 90, 88, 85, 82, 80, 78, 75, 72, 70, 68, 65, 62, 60, 58,
          55, 52, 50, 48, 45, 42, 40, 38, 35, 32, 30, 28, 25, 22, 20, 18, 15, 12, 10, 8, 5, 2, 0, -2,
          150, 155, 160, 165, 170, 175, 180, 185, 190, 195, 200, 205, 210, 215, 220, 225, 230, 235, 240, 245,
          250, 255, 260, 265, 270, 275, 280, 285, 290, 295, 300, 305, 310, 315, 320, 325, 330, 335, 340, 345,
          350, 345, 340, 335, 330, 325, 320, 315, 310, 305, 300, 295, 290, 285, 280, 275, 270, 265, 260, 255,
          250, 245, 240, 235, 230, 225, 220, 215, 210, 205, 200, 195, 190, 185, 180, 175, 170, 165, 160, 155,
          150, 145, 140, 135, 130, 125, 120, 115, 110, 105, 100, 95, 90, 85, 80, 75, 70, 65, 60, 55,
          50, 45, 40, 35, 30, 25, 20, 15, 10, 5, 0, -5, -10, -15, -20, -25, -30, -35, -40, -45
        ],
        type: 'line',
        smooth: false,
        lineStyle: {
          color: '#ff6b35', // 橙色主线
          width: 1,
        },
        itemStyle: {
          color: '#ff6b35',
        },
        symbol: 'none',
        sampling: 'lttb',
      },
    ],
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross',
        lineStyle: {
          color: '#ff6b35',
          width: 1,
        },
      },
      formatter: function (params: any) {
        let result = `${params[0].axisValue}<br/>`;
        params.forEach((param: any) => {
          result += `${param.marker}${param.seriesName}: ${param.value} kW<br/>`;
        });
        return result;
      },
    },
    xAxis: {
      axisTick: {
        show: false,
      },
      boundaryGap: false,
      data: Array.from({ length: 120 }, (_, i) => {
        const minutes = i * 5; // 每5分钟一个点
        const hours = Math.floor(minutes / 60);
        const mins = minutes % 60;
        return `${hours.toString().padStart(2, '0')}:${mins.toString().padStart(2, '0')}`;
      }),
      type: 'category',
      axisLabel: {
        fontSize: 8,
        interval: 11, // 每隔12个显示一个标签（每小时显示一次）
        rotate: 0,
      },
    },
    yAxis: {
      axisTick: {
        show: false,
      },
      type: 'value',
      axisLine: {
        show: false,
      },
      axisLabel: {
        fontSize: 8,
        formatter: '{value}',
        color: '#666',
      },
      splitLine: {
        lineStyle: {
          color: '#e5e7eb',
          type: 'solid',
          width: 0.5,
        },
      },
      min: -50,
      max: 350,
      interval: 50,
    },
  });
});
</script>

<template>
  <EchartsUI ref="chartRef" height="100%" />
</template>
