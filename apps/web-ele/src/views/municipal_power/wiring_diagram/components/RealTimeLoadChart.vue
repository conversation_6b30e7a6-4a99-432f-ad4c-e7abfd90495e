<script lang="ts" setup>
import { onMounted, ref } from 'vue';

import {
  EchartsUI,
  type EchartsUIType,
  useEcharts,
} from '@vben/plugins/echarts';

const chartRef = ref<EchartsUIType>();
const { renderEcharts } = useEcharts(chartRef);

onMounted(() => {
  renderEcharts({
    grid: {
      bottom: '15%',
      containLabel: true,
      left: '5%',
      right: '5%',
      top: '10%',
    },
    legend: {
      bottom: '5%',
      data: ['P', 'Pa', 'Pb', 'Pc'],
      textStyle: {
        fontSize: 10,
      },
    },
    series: [
      {
        name: 'P',
        data: [150, 140, 130, 145, 160, 180, 200, 220, 210, 190, 170, 160, 150, 140, 130, 120, 110, 100, 90, 80, 70, 60, 50, 40],
        type: 'line',
        smooth: true,
        lineStyle: {
          color: '#ff6b35', // 橙色
          width: 2,
        },
        itemStyle: {
          color: '#ff6b35',
        },
        symbol: 'circle',
        symbolSize: 3,
      },
      {
        name: 'Pa',
        data: [120, 115, 110, 125, 140, 155, 170, 185, 175, 160, 145, 135, 125, 115, 105, 95, 85, 75, 65, 55, 45, 35, 25, 15],
        type: 'line',
        smooth: true,
        lineStyle: {
          color: '#4ade80', // 绿色
          width: 2,
        },
        itemStyle: {
          color: '#4ade80',
        },
        symbol: 'circle',
        symbolSize: 3,
      },
      {
        name: 'Pb',
        data: [110, 105, 100, 115, 130, 145, 160, 175, 165, 150, 135, 125, 115, 105, 95, 85, 75, 65, 55, 45, 35, 25, 15, 5],
        type: 'line',
        smooth: true,
        lineStyle: {
          color: '#3b82f6', // 蓝色
          width: 2,
        },
        itemStyle: {
          color: '#3b82f6',
        },
        symbol: 'circle',
        symbolSize: 3,
      },
      {
        name: 'Pc',
        data: [100, 95, 90, 105, 120, 135, 150, 165, 155, 140, 125, 115, 105, 95, 85, 75, 65, 55, 45, 35, 25, 15, 5, -5],
        type: 'line',
        smooth: true,
        lineStyle: {
          color: '#8b5cf6', // 紫色
          width: 2,
        },
        itemStyle: {
          color: '#8b5cf6',
        },
        symbol: 'circle',
        symbolSize: 3,
      },
    ],
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross',
        lineStyle: {
          color: '#ff6b35',
          width: 1,
        },
      },
      formatter: function (params: any) {
        let result = `${params[0].axisValue}<br/>`;
        params.forEach((param: any) => {
          result += `${param.marker}${param.seriesName}: ${param.value} kW<br/>`;
        });
        return result;
      },
    },
    xAxis: {
      axisTick: {
        show: false,
      },
      boundaryGap: false,
      data: [
        '00:00', '01:00', '02:00', '03:00', '04:00', '05:00', 
        '06:00', '07:00', '08:00', '09:00', '10:00', '11:00',
        '12:00', '13:00', '14:00', '15:00', '16:00', '17:00',
        '18:00', '19:00', '20:00', '21:00', '22:00', '23:00'
      ],
      type: 'category',
      axisLabel: {
        fontSize: 9,
        interval: 3, // 每隔3个显示一个标签
      },
    },
    yAxis: {
      axisTick: {
        show: false,
      },
      type: 'value',
      axisLine: {
        show: false,
      },
      axisLabel: {
        fontSize: 9,
        formatter: '{value}',
      },
      splitLine: {
        lineStyle: {
          color: '#f3f4f6',
          type: 'dashed',
        },
      },
      min: 0,
      max: 350,
    },
  });
});
</script>

<template>
  <EchartsUI ref="chartRef" height="100%" />
</template>
