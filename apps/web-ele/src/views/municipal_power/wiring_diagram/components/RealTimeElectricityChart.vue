<script lang="ts" setup>
import { onMounted, ref } from 'vue';

import {
  EchartsUI,
  type EchartsUIType,
  useEcharts,
} from '@vben/plugins/echarts';

const chartRef = ref<EchartsUIType>();
const { renderEcharts } = useEcharts(chartRef);

onMounted(() => {
  renderEcharts({
    grid: {
      bottom: '20%',
      containLabel: true,
      left: '5%',
      right: '5%',
      top: '10%',
    },
    legend: {
      bottom: '5%',
      data: ['分时段', '总用电'],
      textStyle: {
        fontSize: 10,
      },
    },
    series: [
      {
        name: '分时段',
        data: [4200, 3800, 4500, 3200, 2800, 3600, 4100, 3900],
        type: 'bar',
        barWidth: '60%',
        itemStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 0,
                color: '#ff6b35', // 橙色渐变起始
              },
              {
                offset: 1,
                color: '#ff8c42', // 橙色渐变结束
              },
            ],
          },
        },
      },
      {
        name: '总用电',
        data: [3800, 3400, 4100, 2800, 2400, 3200, 3700, 3500],
        type: 'bar',
        barWidth: '60%',
        itemStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 0,
                color: '#4ade80', // 绿色渐变起始
              },
              {
                offset: 1,
                color: '#22c55e', // 绿色渐变结束
              },
            ],
          },
        },
      },
    ],
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow',
      },
      formatter: function (params: any) {
        let result = `${params[0].axisValue}<br/>`;
        params.forEach((param: any) => {
          result += `${param.marker}${param.seriesName}: ${param.value} kWh<br/>`;
        });
        return result;
      },
    },
    xAxis: {
      axisTick: {
        show: false,
      },
      data: ['08-01', '08-05', '08-09', '08-13', '08-17', '08-21', '08-25', '08-29'],
      type: 'category',
      axisLabel: {
        fontSize: 9,
        rotate: 45,
      },
      axisLine: {
        lineStyle: {
          color: '#e5e7eb',
        },
      },
    },
    yAxis: {
      axisTick: {
        show: false,
      },
      type: 'value',
      axisLine: {
        show: false,
      },
      axisLabel: {
        fontSize: 9,
        formatter: '{value}',
      },
      splitLine: {
        lineStyle: {
          color: '#f3f4f6',
          type: 'dashed',
        },
      },
      min: 0,
      max: 5000,
    },
  });
});
</script>

<template>
  <EchartsUI ref="chartRef" height="100%" />
</template>
