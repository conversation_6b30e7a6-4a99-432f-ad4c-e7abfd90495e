<script setup lang="ts">
import { AnalysisChartCard, Page } from '@vben/common-ui';

defineOptions({
  name: 'MunicipalPowerDashboard',
});
</script>

<template>
  <Page auto-content-height>
    <div class="flex h-full flex-col gap-4 overflow-hidden">
      <!-- 上面一行：三列等分布局 -->
      <div class="flex h-1/2 gap-4">
        <!-- 第一列：概况 -->
        <AnalysisChartCard title="概况" class="flex-1">
          <div class="flex h-full items-center justify-center text-muted-foreground">
            概况内容区域
          </div>
        </AnalysisChartCard>

        <!-- 第二列：站点状态 -->
        <AnalysisChartCard title="站点状态" class="flex-1">
          <div class="flex h-full items-center justify-center text-muted-foreground">
            站点状态内容区域
          </div>
        </AnalysisChartCard>

        <!-- 第三列：实时数据 -->
        <AnalysisChartCard title="实时数据" class="flex-1">
          <div class="flex h-full items-center justify-center text-muted-foreground">
            实时数据内容区域
          </div>
        </AnalysisChartCard>
      </div>

      <!-- 下面一行：两列布局 -->
      <div class="flex h-1/2 gap-4">
        <!-- 第一列：占2/3 - 主要图表 -->
        <AnalysisChartCard title="能耗趋势图" class="w-2/3">
          <div class="flex h-full items-center justify-center text-muted-foreground">
            能耗趋势图表区域
          </div>
        </AnalysisChartCard>

        <!-- 第二列：占1/3 - 统计信息 -->
        <AnalysisChartCard title="统计信息" class="w-1/3">
          <div class="flex h-full items-center justify-center text-muted-foreground">
            统计信息区域
          </div>
        </AnalysisChartCard>
      </div>
    </div>
  </Page>
</template>
